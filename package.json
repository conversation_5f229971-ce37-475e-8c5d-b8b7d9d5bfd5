{"name": "rally", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "download": "tsx src/scripts/download.ts", "backtest": "tsx src/scripts/backtest.ts"}, "dependencies": {"@binance/futures-connector": "^0.1.7", "binance": "^2.15.20", "binance-api-node": "^0.12.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "tsx": "^4.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.3", "typescript": "^5"}}