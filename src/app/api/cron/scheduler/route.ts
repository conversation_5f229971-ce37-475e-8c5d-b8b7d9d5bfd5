/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from "next/server";

/**
 * Vercel Cron job endpoint
 * This runs every 5 minutes and triggers the main scheduler
 */
export async function GET() {
  try {
    console.log(`🕐 Vercel Cron triggered - ${new Date().toISOString()}`);
    
    // Get the base URL for internal API calls
    const baseUrl = getBaseUrl();
    
    // Call the main scheduler endpoint
    const response = await fetch(`${baseUrl}/api/scheduler`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vercel-Cron-Scheduler',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Scheduler completed successfully: ${data.scheduler?.action || 'UNKNOWN'}`);
      
      return NextResponse.json({
        success: true,
        cronTriggered: true,
        schedulerResponse: data,
        timestamp: new Date().toISOString(),
      }, { status: 200 });
    } else {
      const errorText = await response.text();
      console.error(`❌ Scheduler failed: ${response.status} ${response.statusText} - ${errorText}`);
      
      return NextResponse.json({
        success: false,
        error: `Scheduler failed: ${response.status} ${response.statusText}`,
        details: errorText,
        timestamp: new Date().toISOString(),
      }, { status: 500 });
    }
    
  } catch (error: any) {
    console.error('🔴 Cron job error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Cron job execution failed',
      details: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Get base URL for internal API calls
 */
function getBaseUrl(): string {
  // Vercel deployment URL
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  
  // Custom domain in production
  if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DOMAIN) {
    return `https://${process.env.NEXT_PUBLIC_DOMAIN}`;
  }
  
  // Local development
  return 'http://localhost:3000';
}