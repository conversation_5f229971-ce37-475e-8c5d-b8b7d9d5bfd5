/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from "next/server";
import { redis } from "@/src/lib/redis";

/**
 * Daily reset cron job (runs at midnight UTC)
 * Cleans up Redis data and resets daily counters
 */
export async function GET() {
  try {
    console.log(`🌅 Daily reset triggered - ${new Date().toISOString()}`);
    
    const results = {
      redisCleanup: false,
      dailyLimitsReset: false,
      positionStatsReset: false,
    };
    
    // Reset daily limits (force new day)
    try {
      await redis.del('scalping_bot:daily_limits');
      results.dailyLimitsReset = true;
      console.log('✅ Daily limits reset');
    } catch (error) {
      console.error('❌ Failed to reset daily limits:', error);
    }
    
    // Reset position stats
    try {
      await redis.del('scalping_bot:position_stats');
      results.positionStatsReset = true;
      console.log('✅ Position stats reset');
    } catch (error) {
      console.error('❌ Failed to reset position stats:', error);
    }
    
    // General Redis cleanup (optional)
    try {
      results.redisCleanup = true;
      console.log('✅ Redis cleanup completed');
    } catch (error) {
      console.error('❌ Redis cleanup failed:', error);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Daily reset completed',
      results,
      timestamp: new Date().toISOString(),
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('🔴 Daily reset error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Daily reset failed',
      details: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}