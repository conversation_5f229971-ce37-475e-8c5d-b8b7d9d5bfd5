/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from "next/server";
import { StrategyTester } from "@/src/app/api/init/test-strategy";
import { getExchangeDetails } from "@/src/functions/index";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const testType = searchParams.get('type') || 'full';
    const symbol = searchParams.get('symbol') || 'BTCUSDT';
    
    console.log(`🧪 Starting strategy test - Type: ${testType}`);
    
    switch (testType) {
      case 'single':
        const singleResult = await StrategyTester.testSymbol(symbol);
        return NextResponse.json({
          success: true,
          testType: 'single',
          result: singleResult,
          timestamp: new Date().toISOString(),
        });
        
      case 'config':
        const configResult = StrategyTester.testConfiguration();
        return NextResponse.json({
          success: true,
          testType: 'configuration',
          result: configResult,
          timestamp: new Date().toISOString(),
        });
        
      case 'benchmark':
        const benchmarkResult = await StrategyTester.benchmarkPerformance(symbol, 5);
        return NextResponse.json({
          success: true,
          testType: 'benchmark',
          result: benchmarkResult,
          timestamp: new Date().toISOString(),
        });
        
      case 'multi':
        const { exchangeSymbols } = await getExchangeDetails();
        const testSymbols = exchangeSymbols.slice(0, 20); // Test first 20 symbols
        const multiResults = await StrategyTester.testMultipleSymbols(testSymbols);
        const stats = StrategyTester.analyzeResults(multiResults);
        
        return NextResponse.json({
          success: true,
          testType: 'multi-symbol',
          results: multiResults,
          statistics: stats,
          timestamp: new Date().toISOString(),
        });
        
      case 'full':
      default:
        const { exchangeSymbols: allSymbols } = await getExchangeDetails();
        const sampleSymbols = allSymbols.slice(0, 10); // Test first 10 symbols for full test
        const fullResults = await StrategyTester.runFullTestSuite(sampleSymbols);
        
        return NextResponse.json({
          success: true,
          testType: 'full-suite',
          results: fullResults,
          timestamp: new Date().toISOString(),
        });
    }
    
  } catch (error: any) {
    console.error('🔴 Strategy test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { symbols, testType = 'multi' } = body;
    
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json({
        success: false,
        error: 'Symbols array is required',
      }, { status: 400 });
    }
    
    console.log(`🧪 Starting custom strategy test on ${symbols.length} symbols`);
    
    const results = await StrategyTester.testMultipleSymbols(symbols);
    const stats = StrategyTester.analyzeResults(results);
    
    return NextResponse.json({
      success: true,
      testType: 'custom',
      results,
      statistics: stats,
      timestamp: new Date().toISOString(),
    });
    
  } catch (error: any) {
    console.error('🔴 Custom strategy test error:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
