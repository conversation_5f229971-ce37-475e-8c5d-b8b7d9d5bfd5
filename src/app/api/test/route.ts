import { NextResponse } from "next/server";

export async function GET() {
  return NextResponse.json({
    success: true,
    message: "Rally Scalping Bot API is working!",
    timestamp: new Date().toISOString(),
    environment: {
      hasRedisUrl: !!process.env.UPSTASH_REDIS_REST_URL,
      hasRedisToken: !!process.env.UPSTASH_REDIS_REST_TOKEN,
      hasBinanceKey: !!process.env.BINANCE_API_KEY,
      hasBinanceSecret: !!process.env.BINANCE_SECRET_KEY,
    }
  }, { status: 200 });
}