/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from "next/server";
import { getAccountDetails, getActivePositions } from "@/src/functions/index";
import { DailyLimitsManager } from "@/src/app/api/init/daily-limits";
import { PositionMonitor } from "@/src/app/api/init/position-monitor";
import { client } from "@/src/config/client";
import { redis } from "@/src/lib/redis";

interface HealthStatus {
  overall: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  components: {
    binanceConnection: 'UP' | 'DOWN';
    accountAccess: 'UP' | 'DOWN';
    positionMonitor: 'UP' | 'DOWN';
    dailyLimits: 'UP' | 'DOWN';
    redisConnection: 'UP' | 'DOWN' | 'UNAVAILABLE';
  };
  metrics: {
    uptime: number;
    lastSuccessfulTrade: number;
    errorRate: number;
    positionHealth: number;
  };
  alerts: string[];
}

/**
 * Health check endpoint for monitoring bot stability
 * Provides comprehensive status of all bot components
 */
export async function GET() {
  const startTime = Date.now();
  const alerts: string[] = [];
  
  try {
    console.log(`🏥 Health Check - ${new Date().toISOString()}`);
    
    // Test Binance connection
    const binanceStatus = await testBinanceConnection();
    
    // Test account access
    const accountStatus = await testAccountAccess();
    
    // Test position monitoring
    const positionStatus = await testPositionMonitoring();
    
    // Test daily limits
    const dailyLimitsStatus = await testDailyLimits();
    
    // Test Redis connection
    const redisStatus = await testRedisConnection();
    
    // Calculate metrics
    const metrics = await calculateHealthMetrics();
    
    // Generate alerts based on status
    if (binanceStatus === 'DOWN') alerts.push('Binance connection failed');
    if (accountStatus === 'DOWN') alerts.push('Account access failed');
    if (positionStatus === 'DOWN') alerts.push('Position monitoring failed');
    if (dailyLimitsStatus === 'DOWN') alerts.push('Daily limits system failed');
    if (redisStatus === 'DOWN') alerts.push('Redis connection failed');
    
    if (metrics.errorRate > 20) alerts.push(`High error rate: ${metrics.errorRate}%`);
    if (metrics.positionHealth < 50) alerts.push(`Position health low: ${metrics.positionHealth}%`);
    
    // Determine overall health
    const criticalDown = [binanceStatus, accountStatus].includes('DOWN');
    const warningDown = [positionStatus, dailyLimitsStatus, redisStatus].includes('DOWN');
    
    let overall: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
    if (criticalDown) {
      overall = 'CRITICAL';
    } else if (warningDown || alerts.length > 0) {
      overall = 'WARNING';
    }
    
    const healthStatus: HealthStatus = {
      overall,
      components: {
        binanceConnection: binanceStatus,
        accountAccess: accountStatus,
        positionMonitor: positionStatus,
        dailyLimits: dailyLimitsStatus,
        redisConnection: redisStatus,
      },
      metrics,
      alerts,
    };
    
    const executionTime = Date.now() - startTime;
    
    console.log(`🏥 Health Check Complete: ${overall} (${executionTime}ms)`);
    if (alerts.length > 0) {
      console.log(`⚠️ Alerts: ${alerts.join(', ')}`);
    }
    
    return NextResponse.json({
      success: true,
      health: healthStatus,
      executionTime,
      timestamp: new Date().toISOString(),
    }, { 
      status: overall === 'CRITICAL' ? 503 : 200 
    });
    
  } catch (error: any) {
    console.error('🔴 Health check failed:', error);
    
    return NextResponse.json({
      success: false,
      health: {
        overall: 'CRITICAL',
        error: error.message,
      },
      timestamp: new Date().toISOString(),
    }, { status: 503 });
  }
}

/**
 * Test Binance API connection
 */
async function testBinanceConnection(): Promise<'UP' | 'DOWN'> {
  try {
    await client.ping();
    return 'UP';
  } catch (error) {
    console.error('Binance connection test failed:', error);
    return 'DOWN';
  }
}

/**
 * Test account access and permissions
 */
async function testAccountAccess(): Promise<'UP' | 'DOWN'> {
  try {
    const { accountBalanceInfo } = await getAccountDetails();
    if (accountBalanceInfo && parseFloat(accountBalanceInfo.availableBalance) >= 0) {
      return 'UP';
    }
    return 'DOWN';
  } catch (error) {
    console.error('Account access test failed:', error);
    return 'DOWN';
  }
}

/**
 * Test position monitoring system
 */
async function testPositionMonitoring(): Promise<'UP' | 'DOWN'> {
  try {
    const stats = await PositionMonitor.getPositionStats();
    const recommendation = await PositionMonitor.getTradingRecommendation();
    
    if (stats && recommendation) {
      return 'UP';
    }
    return 'DOWN';
  } catch (error) {
    console.error('Position monitoring test failed:', error);
    return 'DOWN';
  }
}

/**
 * Test daily limits system
 */
async function testDailyLimits(): Promise<'UP' | 'DOWN'> {
  try {
    const limits = await DailyLimitsManager.getDailyLimits();
    const tradingStatus = await DailyLimitsManager.shouldAllowTrading();
    
    if (limits && tradingStatus) {
      return 'UP';
    }
    return 'DOWN';
  } catch (error) {
    console.error('Daily limits test failed:', error);
    return 'DOWN';
  }
}

/**
 * Test Redis connection
 */
async function testRedisConnection(): Promise<'UP' | 'DOWN' | 'UNAVAILABLE'> {
  try {
    if (!redis.isAvailable()) {
      return 'UNAVAILABLE';
    }
    
    const success = await redis.ping();
    return success ? 'UP' : 'DOWN';
  } catch (error) {
    console.error('Redis connection test failed:', error);
    return 'DOWN';
  }
}

/**
 * Calculate health metrics
 */
async function calculateHealthMetrics(): Promise<{
  uptime: number;
  lastSuccessfulTrade: number;
  errorRate: number;
  positionHealth: number;
}> {
  try {
    // Calculate uptime (simplified - in production, store startup time)
    const uptime = Date.now() - (Date.now() - 24 * 60 * 60 * 1000); // Last 24h
    
    // Get position health
    const { activePositions } = await getActivePositions();
    const positionStats = await PositionMonitor.getPositionStats();
    
    // Calculate position health based on fill rate and performance
    const positionHealth = Math.min(100, (activePositions.length / positionStats.maxPositions) * 100 + 50);
    
    // Get daily stats for error rate calculation
    const dailyStats = await DailyLimitsManager.getDailyStats();
    const errorRate = dailyStats.tradesCount > 0 ? 
      Math.max(0, 100 - dailyStats.winRate) : 0;
    
    // Last successful trade (simplified)
    const lastSuccessfulTrade = Date.now() - (15 * 60 * 1000); // Assume within last 15 min
    
    return {
      uptime: Math.round(uptime / 1000 / 60), // minutes
      lastSuccessfulTrade: Math.round((Date.now() - lastSuccessfulTrade) / 1000 / 60), // minutes ago
      errorRate: Math.round(errorRate),
      positionHealth: Math.round(positionHealth),
    };
  } catch (error) {
    console.error('Error calculating health metrics:', error);
    return {
      uptime: 0,
      lastSuccessfulTrade: 999,
      errorRate: 100,
      positionHealth: 0,
    };
  }
}