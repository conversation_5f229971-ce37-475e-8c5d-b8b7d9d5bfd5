/* eslint-disable @typescript-eslint/no-explicit-any */
import { getActivePositions } from "@/src/functions/index";
import { TRADE_CONFIG } from "@/src/config/presets";
import { redis } from "@/src/lib/redis";

interface PositionStats {
  currentCount: number;
  maxPositions: number;
  fillRate: number; // Percentage of max positions filled
  shouldScan: boolean;
  lastScanTime: number;
  nextScanInterval: number; // milliseconds
  consecutiveEmptyScans: number;
}

interface ScanTiming {
  NORMAL_SCAN: number;      // 15 dakika (normal durumda)
  FAST_SCAN: number;        // 5 dakika (sinyal bulunamadığında)
  POSITION_CHECK: number;   // 2 dakika (pozisyon kontrolü)
  MAX_EMPTY_SCANS: number;  // Max ardışık boş tarama
}

/**
 * Intelligent position monitoring system for continuous scalping
 * Adapts scanning intervals based on market conditions and position fill rate
 */
export class PositionMonitor {
  private static readonly SCAN_TIMING: ScanTiming = {
    NORMAL_SCAN: 15 * 60 * 1000,      // 15 minutes
    FAST_SCAN: 5 * 60 * 1000,         // 5 minutes  
    POSITION_CHECK: 2 * 60 * 1000,    // 2 minutes
    MAX_EMPTY_SCANS: 6                 // Max 6 consecutive empty scans before backing off
  };

  private static readonly REDIS_KEY = 'scalping_bot:position_stats';

  /**
   * Get current position statistics and determine scanning strategy
   */
  static async getPositionStats(): Promise<PositionStats> {
    try {
      const { activePositions } = await getActivePositions();
      const currentCount = activePositions.length;
      const maxPositions = TRADE_CONFIG.MAX_POSITIONS;
      const fillRate = (currentCount / maxPositions) * 100;
      
      const now = Date.now();
      
      // Get last stats from Redis
      const lastStats = await this.getLastStatsFromRedis();
      const lastScanTime = lastStats?.lastScanTime || 0;
      const timeSinceLastScan = now - lastScanTime;
      
      // Determine if we should scan based on position fill rate and timing
      const shouldScan = this.shouldPerformScan(currentCount, maxPositions, timeSinceLastScan);
      
      // Calculate next scan interval based on current conditions
      const nextScanInterval = await this.calculateNextScanInterval(currentCount, maxPositions);
      
      // Track consecutive empty scans for backoff strategy
      const consecutiveEmptyScans = lastStats?.consecutiveEmptyScans || 0;

      const stats: PositionStats = {
        currentCount,
        maxPositions,
        fillRate,
        shouldScan,
        lastScanTime: now,
        nextScanInterval,
        consecutiveEmptyScans
      };

      // Store stats to Redis
      await this.storeStatsToRedis(stats);
      
      return stats;
    } catch (error) {
      console.error('Error getting position stats:', error);
      return this.getDefaultStats();
    }
  }

  /**
   * Determine if a scan should be performed based on position fill rate
   */
  private static shouldPerformScan(
    currentCount: number, 
    maxPositions: number, 
    timeSinceLastScan: number
  ): boolean {
    const fillRate = (currentCount / maxPositions) * 100;
    
    // Always scan if we have room for more positions
    if (currentCount < maxPositions) {
      // If we're below 50% capacity, use normal interval
      if (fillRate < 50) {
        return timeSinceLastScan >= this.SCAN_TIMING.NORMAL_SCAN;
      }
      // If we're 50-80% full, check more frequently
      else if (fillRate < 80) {
        return timeSinceLastScan >= this.SCAN_TIMING.FAST_SCAN;
      }
      // If we're 80%+ full, check very frequently for last few slots
      else {
        return timeSinceLastScan >= this.SCAN_TIMING.POSITION_CHECK;
      }
    }

    // Don't scan if we're at max capacity
    return false;
  }

  /**
   * Calculate optimal next scan interval based on market conditions
   */
  private static async calculateNextScanInterval(currentCount: number, maxPositions: number): Promise<number> {
    const fillRate = (currentCount / maxPositions) * 100;
    const lastStats = await this.getLastStatsFromRedis();
    const consecutiveEmptyScans = lastStats?.consecutiveEmptyScans || 0;

    // Backoff strategy: if we've had many empty scans, slow down
    if (consecutiveEmptyScans >= this.SCAN_TIMING.MAX_EMPTY_SCANS) {
      return this.SCAN_TIMING.NORMAL_SCAN * 2; // 30 minutes
    }

    // Adaptive intervals based on position fill rate
    if (fillRate >= 90) {
      return this.SCAN_TIMING.POSITION_CHECK; // 2 minutes - very aggressive
    } else if (fillRate >= 70) {
      return this.SCAN_TIMING.FAST_SCAN; // 5 minutes - aggressive
    } else if (fillRate >= 40) {
      return this.SCAN_TIMING.FAST_SCAN * 1.5; // 7.5 minutes - moderate
    } else {
      return this.SCAN_TIMING.NORMAL_SCAN; // 15 minutes - conservative
    }
  }

  /**
   * Update scan results - track if scan found signals
   */
  static async updateScanResults(signalsFound: number): Promise<void> {
    try {
      const lastStats = await this.getLastStatsFromRedis();
      if (lastStats) {
        if (signalsFound === 0) {
          lastStats.consecutiveEmptyScans += 1;
        } else {
          lastStats.consecutiveEmptyScans = 0; // Reset counter on successful scan
        }
        
        await this.storeStatsToRedis(lastStats);
        console.log(`📊 Scan results updated: ${signalsFound} signals, ${lastStats.consecutiveEmptyScans} consecutive empty scans`);
      }
    } catch (error) {
      console.error('Error updating scan results:', error);
    }
  }

  /**
   * Get trading recommendation based on current position state
   */
  static async getTradingRecommendation(): Promise<{
    action: 'SCAN' | 'WAIT' | 'MONITOR';
    reason: string;
    waitTime: number;
    strategy: 'AGGRESSIVE' | 'MODERATE' | 'CONSERVATIVE';
  }> {
    try {
      const stats = await this.getPositionStats();
      
      if (stats.currentCount >= stats.maxPositions) {
        return {
          action: 'MONITOR',
          reason: 'At maximum position capacity',
          waitTime: this.SCAN_TIMING.POSITION_CHECK,
          strategy: 'CONSERVATIVE'
        };
      }

      if (stats.shouldScan) {
        const strategy = this.getStrategy(stats.fillRate, stats.consecutiveEmptyScans);
        return {
          action: 'SCAN',
          reason: `Position fill rate: ${stats.fillRate.toFixed(1)}%`,
          waitTime: 0,
          strategy
        };
      }

      return {
        action: 'WAIT',
        reason: `Too soon since last scan (${Math.round((Date.now() - stats.lastScanTime) / 1000)}s ago)`,
        waitTime: stats.nextScanInterval,
        strategy: 'MODERATE'
      };
    } catch (error) {
      console.error('Error getting trading recommendation:', error);
      return {
        action: 'WAIT',
        reason: 'Error occurred, backing off',
        waitTime: this.SCAN_TIMING.NORMAL_SCAN,
        strategy: 'CONSERVATIVE'
      };
    }
  }

  /**
   * Determine trading strategy based on current conditions
   */
  private static getStrategy(fillRate: number, consecutiveEmptyScans: number): 'AGGRESSIVE' | 'MODERATE' | 'CONSERVATIVE' {
    if (consecutiveEmptyScans >= this.SCAN_TIMING.MAX_EMPTY_SCANS) {
      return 'CONSERVATIVE';
    }
    
    if (fillRate < 30) {
      return 'AGGRESSIVE';   // Lots of room, be aggressive
    } else if (fillRate < 70) {
      return 'MODERATE';     // Moderate fill, balanced approach
    } else {
      return 'CONSERVATIVE'; // Nearly full, be selective
    }
  }

  /**
   * Get last stats from Redis
   */
  private static async getLastStatsFromRedis(): Promise<PositionStats | null> {
    try {
      const stats = await redis.get(this.REDIS_KEY);
      return stats;
    } catch (error) {
      console.error('Error getting stats from Redis:', error);
      return null;
    }
  }

  /**
   * Store stats to Redis
   */
  private static async storeStatsToRedis(stats: PositionStats): Promise<void> {
    try {
      // Store with 6 hour expiration
      await redis.set(this.REDIS_KEY, stats, 6 * 60 * 60);
    } catch (error) {
      console.error('Error storing stats to Redis:', error);
    }
  }

  /**
   * Get default stats in case of error
   */
  private static getDefaultStats(): PositionStats {
    return {
      currentCount: 0,
      maxPositions: TRADE_CONFIG.MAX_POSITIONS,
      fillRate: 0,
      shouldScan: true,
      lastScanTime: 0,
      nextScanInterval: this.SCAN_TIMING.NORMAL_SCAN,
      consecutiveEmptyScans: 0
    };
  }

  /**
   * Log current position status for monitoring
   */
  static async logPositionStatus(): Promise<void> {
    try {
      const stats = await this.getPositionStats();
      const recommendation = await this.getTradingRecommendation();
      
      console.log(`📊 Position Monitor Status:`);
      console.log(`   Positions: ${stats.currentCount}/${stats.maxPositions} (${stats.fillRate.toFixed(1)}%)`);
      console.log(`   Recommendation: ${recommendation.action} (${recommendation.strategy})`);
      console.log(`   Reason: ${recommendation.reason}`);
      console.log(`   Empty scans: ${stats.consecutiveEmptyScans}`);
      console.log(`   Next check in: ${Math.round(recommendation.waitTime / 1000 / 60)} minutes`);
    } catch (error) {
      console.error('Error logging position status:', error);
    }
  }
}