/* eslint-disable @typescript-eslint/no-explicit-any */
import { analyzeSymbol } from "@/src/app/api/init/analyze";
import { openPosition } from "@/src/app/api/init/position";
import {
    getActivePositions,
    getExchangeDetails,
    isPositionOpen,
    getAccountDetails,
} from "@/src/functions/index";
import { CURRENT_STRATEGY, SignalType, TRADE_CONFIG } from "@/src/config/presets";
import { NextResponse } from "next/server";
import { AdvancedPositionManager } from "@/src/app/api/init/advanced-position-management";
import { StrategyTester } from "@/src/app/api/init/test-strategy";
import { client } from "@/src/config/client";
import { DailyLimitsManager } from "@/src/app/api/init/daily-limits";
import { PositionMonitor } from "@/src/app/api/init/position-monitor";


export async function GET(request: Request) {
    try {
        // Check if this is a manual scan request
        const url = new URL(request.url);
        const isManualScan = url.searchParams.get('manual') === 'true';
        
        // Log current position status
        await PositionMonitor.logPositionStatus();
        
        let recommendation;
        
        if (!isManualScan) {
            // Check if we should perform a scan based on position monitoring
            recommendation = await PositionMonitor.getTradingRecommendation();
            
            if (recommendation.action === 'WAIT') {
                console.log(`⏳ ${recommendation.reason}`);
                return NextResponse.json({
                    success: true,
                    action: 'WAIT',
                    message: recommendation.reason,
                    waitTime: recommendation.waitTime,
                    strategy: recommendation.strategy,
                    positionStats: await PositionMonitor.getPositionStats(),
                    timestamp: new Date().toISOString(),
                }, { status: 200 });
            }

            if (recommendation.action === 'MONITOR') {
                console.log(`👀 ${recommendation.reason}`);
                return NextResponse.json({
                    success: true,
                    action: 'MONITOR',
                    message: recommendation.reason,
                    positionStats: await PositionMonitor.getPositionStats(),
                    timestamp: new Date().toISOString(),
                }, { status: 200 });
            }
        } else {
            console.log(`🔧 Manual scan requested - bypassing position monitor checks`);
            recommendation = {
                action: 'SCAN',
                reason: 'Manual scan requested',
                strategy: 'MANUAL',
                waitTime: 0
            };
        }

        // Check daily limits (more lenient for manual scans)
        const tradingStatus = await DailyLimitsManager.shouldAllowTrading();
        if (!tradingStatus.allowed && !isManualScan) {
            console.log(`🚫 Trading disabled: ${tradingStatus.reason}`);
            return NextResponse.json({
                success: false,
                error: `Trading disabled: ${tradingStatus.reason}`,
                dailyLimits: await DailyLimitsManager.getDailyLimits(),
                timestamp: new Date().toISOString(),
            }, { status: 200 });
        } else if (!tradingStatus.allowed && isManualScan) {
            console.log(`⚠️ Manual scan despite daily limits: ${tradingStatus.reason}`);
        }

    const { accountBalanceInfo } = await getAccountDetails();
    if (parseFloat(accountBalanceInfo.availableBalance) < TRADE_CONFIG.MIN_USDT_BALANCE) {
        console.log(`⚠️ Insufficient balance: ${(accountBalanceInfo.availableBalance)} USDT`);
        return insufficientBalance;
    }

    const { positions, activePositions } = await getActivePositions();
    console.log(`🔍 Current positions: ${activePositions.length}/${TRADE_CONFIG.MAX_POSITIONS}`);

    if (activePositions.length >= TRADE_CONFIG.MAX_POSITIONS) {
        console.log(`⚠️ Maksimum pozisyon sayısı aşıldı: ${activePositions.length}/${TRADE_CONFIG.MAX_POSITIONS}`);
        return maxPositions;
    }

    const { exchangeSymbols, exchangeInfo } = await getExchangeDetails();

    try {
        const analysisResults = [];
        let openedPositions = activePositions.length;

        // Filter top volume symbols for better performance and quality
        const topVolumeSymbols = await getTopVolumeSymbols(exchangeSymbols || [], 300);
        console.log(`📊 Analyzing top ${topVolumeSymbols.length} volume symbols out of ${exchangeSymbols?.length} total`);

        // Process symbols with controlled parallelism for better performance
        const batchSize = 10; // Process 10 symbols in parallel
        const symbolBatches = [];
        
        for (let i = 0; i < topVolumeSymbols.length; i += batchSize) {
            symbolBatches.push(topVolumeSymbols.slice(i, i + batchSize));
        }

        console.log(`⚡ Processing ${topVolumeSymbols.length} symbols in ${symbolBatches.length} batches of ${batchSize}`);

        for (const batch of symbolBatches) {
            // Check if we've reached position limit
            if (openedPositions >= TRADE_CONFIG.MAX_POSITIONS) {
                console.log(`🛑 Position limit reached: ${openedPositions}/${TRADE_CONFIG.MAX_POSITIONS}`);
                break;
            }

            // Process batch in parallel for analysis
            const batchPromises = batch.map(async (symbol) => {
                try {
                    if (isPositionOpen(positions, symbol)) {
                        console.log(`${symbol}: ⚠️ Zaten açık pozisyon var`);
                        return null;
                    }

                    const signalData = await analyzeSymbol(symbol);
                    if (!signalData?.positionSide) {
                        console.log(`${symbol}:❌ Sinyal bulunamadı`);
                        return null;
                    }

                    return signalData;
                } catch (error: any) {
                    console.error(`🔴${symbol} ANALYZE ERROR:`, error);
                    return null;
                }
            });

            const batchResults = await Promise.all(batchPromises);
            const validSignals = batchResults.filter(result => result !== null);

            // Process valid signals sequentially to avoid race conditions in position opening
            for (const signalData of validSignals) {
                if (openedPositions >= TRADE_CONFIG.MAX_POSITIONS) {
                    console.log(`🛑 Position limit reached during processing: ${openedPositions}/${TRADE_CONFIG.MAX_POSITIONS}`);
                    break;
                }

                try {
                    const positionResult = await openPosition(
                        signalData.symbol,
                        signalData.positionSide as SignalType,
                        exchangeInfo
                    );
                    console.log("✅ positionResult", positionResult);
                    openedPositions++; // Increment counter after successful position

                    // Debug: Check real vs local count
                    const { activePositions: currentPositions } = await getActivePositions();
                    console.log(`📊 Position Count - Local: ${openedPositions}, Real: ${currentPositions.length}`);

                    analysisResults.push(signalData);
                } catch (error) {
                    console.error(`🔴${signalData.symbol} POSITION ERROR:`, error);
                }
            }

            // Small delay between batches to prevent API rate limiting
            if (symbolBatches.indexOf(batch) < symbolBatches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        console.log(`🏁 Trading completed. Opened ${openedPositions - activePositions.length} new positions`);
        console.log("validSignals", analysisResults);
        
        // Update scan results for position monitor
        await PositionMonitor.updateScanResults(analysisResults.length);
        
        // Get updated daily limits for response
        const dailyLimits = await DailyLimitsManager.getDailyLimits();
        
        return validSignalsResponse(analysisResults, activePositions, exchangeSymbols, dailyLimits, recommendation.strategy, isManualScan);
    } catch (error: any) {
        console.error("🔴INNER TRADING ERROR:", error);
        return NextResponse.json(
            {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
    } catch (error: any) {
        console.error("🔴MAIN FUNCTION ERROR:", error);
        return NextResponse.json(
            {
                success: false,
                error: `Main function error: ${error.message}`,
                timestamp: new Date().toISOString(),
            },
            { status: 500 }
        );
    }
}

const validSignalsResponse = (validSignals: any, activePositions: any, exchangeSymbols: any, dailyLimits?: any, strategy?: string, isManual?: boolean) => NextResponse.json(
    {
        success: true,
        action: 'SCAN_COMPLETED',
        scanType: isManual ? 'MANUAL' : 'AUTOMATIC',
        data: {
            config: TRADE_CONFIG,
            strategy: CURRENT_STRATEGY,
            tradingStrategy: strategy,
            signalsFound: validSignals.length,
            activePositionsLength: activePositions.length,
            validSignals: validSignals, 
            activePositions: activePositions,
            totalSymbols: exchangeSymbols?.length,
            dailyLimits: dailyLimits,
            isManualScan: isManual,
            timestamp: new Date().toISOString(),
        },
    },
    { status: 200 } )   


const insufficientBalance = NextResponse.json(
    {
        success: false,
        error: "Insufficient balance",
        timestamp: new Date().toISOString(),
    },
    { status: 500 }
);

const maxPositions = NextResponse.json(
    {
        success: false,
        error: "Maksimum pozisyon sayısı aşıldı",
        timestamp: new Date().toISOString(),
    },
    { status: 500 }
);

// Get top volume symbols for better performance and signal quality
async function getTopVolumeSymbols(allSymbols: string[], limit: number): Promise<string[]> {
    try {
        // Get 24hr ticker statistics
        const tickerResponse = await client.futuresDailyStats();
        
        // Ensure tickers is an array
        const tickers = Array.isArray(tickerResponse) ? tickerResponse : [tickerResponse];
        
        // Filter only USDT pairs and sort by volume
        const usdtTickers = tickers
            .filter((ticker: any) => 
                ticker?.symbol?.endsWith('USDT') && 
                allSymbols.includes(ticker.symbol) &&
                parseFloat(ticker.quoteVolume || '0') > 10000000 // Min 10M USDT volume
            )
            .sort((a: any, b: any) => parseFloat(b.quoteVolume || '0') - parseFloat(a.quoteVolume || '0'))
            .slice(0, limit)
            .map((ticker: any) => ticker.symbol);

        console.log(`🔝 Top volume symbols: ${usdtTickers.slice(0, 10).join(', ')}...`);
        return usdtTickers;
    } catch (error) {
        console.error('Error getting top volume symbols:', error);
        // Fallback to first N symbols if API fails
        return allSymbols.slice(0, limit);
    }
}