/* eslint-disable @typescript-eslint/no-explicit-any */
import { analyzeSymbol } from "./analyze";
import { TRADE_CONFIG } from "@/src/config/presets";

/**
 * Test suite for the improved scalping strategy
 */
export class StrategyTester {
  
  /**
   * Test the strategy on a single symbol
   */
  static async testSymbol(symbol: string): Promise<any> {
    try {
      console.log(`🧪 Testing strategy on ${symbol}...`);
      
      const startTime = Date.now();
      const result = await analyzeSymbol(symbol);
      const endTime = Date.now();
      
      const testResult = {
        symbol,
        executionTime: endTime - startTime,
        hasSignal: !!result,
        signalType: result?.positionSide || null,
        technicalData: result?.technicalData || null,
        timestamp: new Date().toISOString(),
      };
      
      if (result) {
        console.log(`✅ ${symbol}: ${result.positionSide} signal detected`);
        console.log(`   RSI: ${result.technicalData.rsi}/${result.technicalData.rsi5min}`);
        console.log(`   Volume: ${result.technicalData.volumeRatio}x`);
        console.log(`   Volatility: ${result.technicalData.bandDiff}%`);
        console.log(`   Momentum: ${result.technicalData.priceChange}%`);
      } else {
        console.log(`❌ ${symbol}: No signal`);
      }
      
      return testResult;
    } catch (error: any) {
      console.error(`🔴 Test error for ${symbol}:`, error.message);
      return {
        symbol,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Test strategy on multiple symbols
   */
  static async testMultipleSymbols(symbols: string[]): Promise<any[]> {
    console.log(`🧪 Testing strategy on ${symbols.length} symbols...`);
    
    const results = [];
    const batchSize = 10; // Process in batches to avoid rate limit
    
    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      const batchPromises = batch.map(symbol => this.testSymbol(symbol));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches
        if (i + batchSize < symbols.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Batch processing error:`, error);
      }
    }
    
    return results;
  }

  /**
   * Analyze test results and provide statistics
   */
  static analyzeResults(results: any[]): any {
    const validResults = results.filter(r => !r.error);
    const signalResults = validResults.filter(r => r.hasSignal);
    
    const stats = {
      totalTested: results.length,
      validTests: validResults.length,
      signalsGenerated: signalResults.length,
      signalRate: validResults.length > 0 ? (signalResults.length / validResults.length) * 100 : 0,
      longSignals: signalResults.filter(r => r.signalType === 'LONG').length,
      shortSignals: signalResults.filter(r => r.signalType === 'SHORT').length,
      averageExecutionTime: validResults.length > 0 
        ? validResults.reduce((sum, r) => sum + (r.executionTime || 0), 0) / validResults.length 
        : 0,
      errors: results.filter(r => r.error).length,
    };

    console.log('\n📊 Strategy Test Results:');
    console.log(`   Total symbols tested: ${stats.totalTested}`);
    console.log(`   Valid tests: ${stats.validTests}`);
    console.log(`   Signals generated: ${stats.signalsGenerated}`);
    console.log(`   Signal rate: ${stats.signalRate.toFixed(1)}%`);
    console.log(`   Long signals: ${stats.longSignals}`);
    console.log(`   Short signals: ${stats.shortSignals}`);
    console.log(`   Average execution time: ${stats.averageExecutionTime.toFixed(0)}ms`);
    console.log(`   Errors: ${stats.errors}`);

    return stats;
  }

  /**
   * Performance benchmark test
   */
  static async benchmarkPerformance(symbol: string, iterations: number = 10): Promise<any> {
    console.log(`⚡ Benchmarking performance on ${symbol} (${iterations} iterations)...`);
    
    const times: number[] = [];
    let successCount = 0;
    
    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        await analyzeSymbol(symbol);
        const endTime = Date.now();
        
        times.push(endTime - startTime);
        successCount++;
      } catch (error) {
        console.error(`Benchmark iteration ${i + 1} failed:`, error);
      }
      
      // Small delay between iterations
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    const benchmark = {
      symbol,
      iterations,
      successCount,
      successRate: (successCount / iterations) * 100,
      averageTime: avgTime,
      minTime,
      maxTime,
      standardDeviation: Math.sqrt(
        times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / times.length
      ),
    };
    
    console.log(`📈 Benchmark Results for ${symbol}:`);
    console.log(`   Success rate: ${benchmark.successRate.toFixed(1)}%`);
    console.log(`   Average time: ${benchmark.averageTime.toFixed(0)}ms`);
    console.log(`   Min time: ${benchmark.minTime}ms`);
    console.log(`   Max time: ${benchmark.maxTime}ms`);
    console.log(`   Std deviation: ${benchmark.standardDeviation.toFixed(1)}ms`);
    
    return benchmark;
  }

  /**
   * Test strategy configuration parameters
   */
  static testConfiguration(): any {
    console.log('⚙️  Testing strategy configuration...');
    
    const config = {
      strategy: 'optimizedScalp',
      parameters: {
        maxPositions: TRADE_CONFIG.MAX_POSITIONS,
        positionSize: TRADE_CONFIG.POSITION_SIZE,
        leverage: TRADE_CONFIG.LEVERAGE,
        stopLoss: TRADE_CONFIG.STOP_LOSS_PERCENT,
        takeProfit: TRADE_CONFIG.TAKE_PROFIT_PERCENT,
        riskRewardRatio: TRADE_CONFIG.TAKE_PROFIT_PERCENT / TRADE_CONFIG.STOP_LOSS_PERCENT,
        bbPeriod: TRADE_CONFIG.BB_PERIOD,
        rsiPeriod: TRADE_CONFIG.RSI_PERIOD,
        volatilityThreshold: TRADE_CONFIG.VOLATILITY_THRESHOLD,
      },
      validation: {
        riskRewardValid: (TRADE_CONFIG.TAKE_PROFIT_PERCENT / TRADE_CONFIG.STOP_LOSS_PERCENT) >= 1.5,
        leverageValid: TRADE_CONFIG.LEVERAGE <= 20 && TRADE_CONFIG.LEVERAGE >= 5,
        positionSizeValid: TRADE_CONFIG.POSITION_SIZE <= 1.0 && TRADE_CONFIG.POSITION_SIZE >= 0.1,
        maxPositionsValid: TRADE_CONFIG.MAX_POSITIONS <= 10 && TRADE_CONFIG.MAX_POSITIONS >= 1,
      }
    };
    
    console.log('📋 Configuration Analysis:');
    console.log(`   Risk/Reward Ratio: ${config.parameters.riskRewardRatio.toFixed(2)} ${config.validation.riskRewardValid ? '✅' : '❌'}`);
    console.log(`   Leverage: ${config.parameters.leverage}x ${config.validation.leverageValid ? '✅' : '❌'}`);
    console.log(`   Position Size: ${config.parameters.positionSize} ${config.validation.positionSizeValid ? '✅' : '❌'}`);
    console.log(`   Max Positions: ${config.parameters.maxPositions} ${config.validation.maxPositionsValid ? '✅' : '❌'}`);
    
    return config;
  }

  /**
   * Run comprehensive test suite
   */
  static async runFullTestSuite(testSymbols: string[] = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']): Promise<any> {
    console.log('🚀 Running comprehensive strategy test suite...\n');
    
    // 1. Configuration test
    const configTest = this.testConfiguration();
    
    // 2. Performance benchmark
    const benchmark = await this.benchmarkPerformance(testSymbols[0], 5);
    
    // 3. Multi-symbol test
    const multiTest = await this.testMultipleSymbols(testSymbols);
    const stats = this.analyzeResults(multiTest);
    
    const fullResults = {
      timestamp: new Date().toISOString(),
      configuration: configTest,
      benchmark,
      multiSymbolTest: {
        results: multiTest,
        statistics: stats,
      },
      summary: {
        configurationValid: Object.values(configTest.validation).every(v => v),
        performanceAcceptable: benchmark.averageTime < 5000, // Less than 5 seconds
        signalGenerationHealthy: stats.signalRate > 5 && stats.signalRate < 50, // 5-50% signal rate
        overallHealth: 'pending'
      }
    };
    
    // Calculate overall health
    const healthChecks = [
      fullResults.summary.configurationValid,
      fullResults.summary.performanceAcceptable,
      fullResults.summary.signalGenerationHealthy
    ];
    
    const healthyCount = healthChecks.filter(check => check).length;
    fullResults.summary.overallHealth = healthyCount === 3 ? 'excellent' : 
                                       healthyCount === 2 ? 'good' : 
                                       healthyCount === 1 ? 'fair' : 'poor';
    
    console.log(`\n🏁 Test Suite Complete - Overall Health: ${fullResults.summary.overallHealth.toUpperCase()}`);
    
    return fullResults;
  }
}
