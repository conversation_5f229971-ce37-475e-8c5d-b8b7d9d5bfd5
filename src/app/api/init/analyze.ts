/* eslint-disable @typescript-eslint/no-explicit-any */
import { TRADE_CONFIG, SignalType } from "@/src/config/presets";
import { getKlines } from "@/src/functions/index";
import { CandleChartInterval_LT } from "binance-api-node";

export async function analyzeSymbol(symbol: string) {
  const calculatedKlineData = await getCalculatedKlineData(symbol);

  if (!calculatedKlineData) {
    console.log(symbol, "Kline verisi alınamadı");
    return null
  };

  const {
    currentPrice,
    upperBand,
    lowerBand,
    rsi,
    closes,
    currentVolume,
    avgVolume,
    volumeRatio,
  } = calculatedKlineData;

  // For scalping, use 5min RSI instead of 15min (faster signals)
  const rsi5min = await calculateRsiForTimeframe(symbol, "5m");
  if (!rsi5min) {
    console.log(symbol, "5min RSI verisi alınamadı");
    return null;
  }

  let signal = null;
  const bandDiff = ((upperBand - lowerBand) / lowerBand) * 100;

  // Micro-scalping thresholds - Balanced for frequent quality signals
  const baseVolatilityThreshold = 0.05; // 0.05% minimum volatility (moderate)
  const dynamicVolatilityThreshold = Math.max(baseVolatilityThreshold, bandDiff * 0.05);

  // Moderate momentum filter for micro-scalping
  const priceChange = ((currentPrice - closes[closes.length - 2]) / closes[closes.length - 2]) * 100;
  const minimumMomentum = 0.005; // Small movement required (0.005%)

  // Moderate volume filter for micro-scalping
  const minimumVolumeRatio = 0.3; // Volume should be 30% of average

  // OPTIMIZED SCALPING RSI thresholds - Tighter for better signal quality
  // 1min: Tighter thresholds for quality entries
  const rsi1minThresholdLow = 30;  // Strong oversold
  const rsi1minThresholdHigh = 70; // Strong overbought

  // 5min: Stronger confirmation filter
  const rsi5minThresholdLow = 35;  // Stronger confirmation
  const rsi5minThresholdHigh = 65; // Stronger confirmation

  // Debug logging for missed signals
  const debugInfo = {
    rsi1min: rsi,
    rsi5min: rsi5min,
    currentPrice,
    upperBand,
    lowerBand,
    bandDiff,
    priceChange,
    dynamicVolatilityThreshold
  };

  if (
    rsi < rsi1minThresholdLow &&                    // 1min RSI oversold
    rsi5min < rsi5minThresholdLow &&               // 5min RSI also oversold
    currentPrice <= upperBand &&                    // Price near or below upper band (relaxed)
    bandDiff > dynamicVolatilityThreshold &&       // Sufficient volatility
    Math.abs(priceChange) > minimumMomentum &&     // Sufficient momentum
    volumeRatio > minimumVolumeRatio               // Above minimum volume
  ) {
    signal = SignalType.LONG;
  } else if (
    rsi > rsi1minThresholdHigh &&                   // 1min RSI overbought
    rsi5min > rsi5minThresholdHigh &&              // 5min RSI also overbought
    currentPrice >= lowerBand &&                    // Price near or above lower band (relaxed)
    bandDiff > dynamicVolatilityThreshold &&       // Sufficient volatility
    Math.abs(priceChange) > minimumMomentum &&     // Sufficient momentum
    volumeRatio > minimumVolumeRatio               // Above minimum volume
  ) {
    signal = SignalType.SHORT;
  }

  // Enhanced debug logging
  if (signal) {
    console.log(`🎯 ${symbol} SIGNAL: ${signal} - 1min RSI: ${rsi.toFixed(2)}, 5min RSI: ${rsi5min.toFixed(2)}`);
  } else {
    // Log why signal was missed (for debugging)
    const reasons = [];
    if (rsi >= rsi1minThresholdLow && rsi <= rsi1minThresholdHigh) reasons.push(`RSI1 neutral (${rsi.toFixed(1)})`);
    if (rsi5min >= rsi5minThresholdLow && rsi5min <= rsi5minThresholdHigh) reasons.push(`RSI5 neutral (${rsi5min.toFixed(1)})`);
    if (bandDiff <= dynamicVolatilityThreshold) reasons.push(`Low volatility (${bandDiff.toFixed(3)}%)`);
    if (Math.abs(priceChange) <= minimumMomentum) reasons.push(`Low momentum (${priceChange.toFixed(3)}%)`);
    if (volumeRatio <= minimumVolumeRatio) reasons.push(`Low volume (${volumeRatio.toFixed(1)}x)`);

    // Always log for debugging
    console.log(`⚪ ${symbol} - No signal: ${reasons.join(", ")} | Thresholds: RSI1(${rsi1minThresholdLow}-${rsi1minThresholdHigh}) RSI5(${rsi5minThresholdLow}-${rsi5minThresholdHigh}) Vol(${dynamicVolatilityThreshold.toFixed(3)}%) Mom(${minimumMomentum.toFixed(3)}%)`);
  }

  // For micro-scalping, skip higher timeframe confirmation for speed
  // Use the 1min signal directly since we already have 5min RSI confirmation
  const higherTimeframeSignal = signal; // Use same signal for micro-scalping

  console.log(`🎯 ${symbol} SIGNAL: ${signal} - 1min RSI: ${rsi.toFixed(2)}, 5min RSI: ${rsi5min.toFixed(2)}`);

  if (Boolean(higherTimeframeSignal)) {
    const signalData = {
      symbol,
      currentPrice,
      technicalData: {
        rsi: Number(rsi.toFixed(2)),
        rsi5min: Number(rsi5min.toFixed(2)),
        bandDiff: Number(bandDiff.toFixed(2)),
        upperBand: Number(upperBand.toFixed(8)),
        lowerBand: Number(lowerBand.toFixed(8)),
        priceChange: Number(priceChange.toFixed(3)),
        volumeRatio: Number(volumeRatio.toFixed(2)),
        dynamicVolatilityThreshold: Number(dynamicVolatilityThreshold.toFixed(2)),
      },
      oneMinuteSignal: signal,
      higherTimeframeSignal: higherTimeframeSignal as SignalType,
      positionSide: signal === SignalType.LONG && higherTimeframeSignal === SignalType.LONG ? "LONG" : signal === SignalType.SHORT && higherTimeframeSignal === SignalType.SHORT ? "SHORT" : "",
      longSignal: signal === SignalType.LONG && higherTimeframeSignal === SignalType.LONG,
      shortSignal: signal === SignalType.SHORT && higherTimeframeSignal === SignalType.SHORT,
      message: "Aggressive scalping signal - no trend filter applied",
      timestamp: new Date().toISOString(),
    };
    return signalData;
  }
}

const higherTimeframeConfirmation = async ({
  symbol,
  limit,
  currentPrice,
  interval,
  signal,
}: {
  symbol: string;
  limit: number;
  currentPrice: number;
  interval: CandleChartInterval_LT;
  signal: SignalType | null;
}) => {
  // Simple trend filter using EMA
  try {
    const klineData = await klinesData(symbol, interval, 50); // Need more data for EMA
    if (!klineData) return null;

    const { closes } = klineData;
    const ema9 = calculateEMA(closes, 9);
    const ema21 = calculateEMA(closes, 21);

    // Trend direction
    const isUptrend = ema9 > ema21;
    const isDowntrend = ema9 < ema21;

    // Only allow signals in trend direction
    if (signal === SignalType.LONG && isUptrend) {
      return SignalType.LONG;
    } else if (signal === SignalType.SHORT && isDowntrend) {
      return SignalType.SHORT;
    }

    return null; // Signal against trend
  } catch (error) {
    console.error(`Trend filter error for ${symbol}:`, error);
    return signal; // Fallback to original signal if trend filter fails
  }
};

async function getCalculatedKlineData(symbol: string) {
  try {
    const limit = TRADE_CONFIG.KLINE_LIMIT;
    const klineOneMinuteData = await klinesData(symbol, "1m", limit);

    if (!klineOneMinuteData) {
      console.log(symbol, "Kline verisi alınamadı");
    }

    const { closes, volumes } = klineOneMinuteData as any;


    const { upperBand, lowerBand } = bollingerBands({
      period: TRADE_CONFIG.BB_PERIOD,
      stdDev: TRADE_CONFIG.STD_DEV,
      closes,
    });

    const rsi = calculateRsi({
      closes,
      rsiPeriod: TRADE_CONFIG.RSI_PERIOD,
    });

    // Volume analysis with safety checks
    const recentVolumes = volumes.slice(-20).filter((vol: number) => vol > 0);
    const avgVolume = recentVolumes.length > 0
      ? recentVolumes.reduce((sum: number, vol: number) => sum + vol, 0) / recentVolumes.length
      : 1;
    const currentVolume = volumes[volumes.length - 1] || 1;
    const volumeRatio = avgVolume > 0 ? currentVolume / avgVolume : 1;

    return {
      currentPrice: closes[closes.length - 1],
      upperBand,
      lowerBand,
      rsi,
      closes,
      currentVolume,
      avgVolume,
      volumeRatio,
    };
  } catch (error: any) {
    console.error(`🔴 Kline data error for ${symbol}:`, error);
  }
}

const klinesData = async (
  symbol: string,
  interval: CandleChartInterval_LT,
  limit: number
) => {
  try {
    const klines = await getKlines({ symbol, limit, interval });

    if (!klines || klines.length === 0) {
      console.log(symbol, "🔴Klines verisi alınamadı");
    }

    const closes = klines.map((k) => parseFloat(k.close));
    const volumes = klines.map((k) => parseFloat(k.volume));

    const highs = klines.map((k) => (k?.high ? parseFloat(k.high) : 0));
    const lows = klines.map((k) => (k?.low ? parseFloat(k.low) : 0));
    const opens = klines.map((k) => (k?.open ? parseFloat(k.open) : 0));

    return {
      closes,
      volumes,
      highs,
      lows,
      opens,
      timestamps: klines.map((k) => k?.openTime || 0),
    };
  } catch (error: any) {
    console.error("Klines verisi alınırken hata:", error);
    throw new Error(`Klines verisi alınırken hata: ${error.message}`);
  }
};

const bollingerBands = ({
  period,
  stdDev,
  closes,
}: {
  period: number;
  stdDev: number;
  closes: number[];
}): {
  upperBand: number;
  lowerBand: number;
  sma: number;
  middleBand: number;
} => {
  if (closes.length < period) {
    throw new Error(
      `Bollinger Bantlarını hesaplamak için en az ${period} kapanış fiyatı gerekli.`
    );
  }

  if (period <= 0 || stdDev <= 0) {
    throw new Error("Period ve standart sapma değerleri pozitif olmalıdır.");
  }

  if (closes.some(isNaN)) {
    throw new Error("Kapanış fiyatlarının tümü sayı olmalıdır.");
  }

  // Son 'period' kadar kapanış fiyatını al
  const recentPrices = closes.slice(-period);

  // SMA Hesaplama
  const sma = recentPrices.reduce((a, b) => a + b, 0) / period;

  // Standart Sapma Hesaplama
  const squaredDifferences = recentPrices.map((price) =>
    Math.pow(price - sma, 2)
  );
  const deviation = Math.sqrt(
    squaredDifferences.reduce((a, b) => a + b, 0) / period
  );

  // Bantları Hesapla
  const upperBand = sma + stdDev * deviation;
  const lowerBand = sma - stdDev * deviation;
  const middleBand = sma; // TODO

  return {
    upperBand,
    lowerBand,
    middleBand,
    sma, // Opsiyonel olarak SMA'yı da döndürüyoruz
  };
};

const calculateRsi = ({
  closes,
  rsiPeriod,
}: {
  closes: number[];
  rsiPeriod: number;
}): number => {
  if (closes.length < rsiPeriod + 1) {
    throw new Error("Yeterli veri yok");
  }

  const changes = closes.slice(1).map((price, i) => price - closes[i]);
  const gains = changes.map((change) => (change > 0 ? change : 0));
  const losses = changes.map((change) => (change < 0 ? Math.abs(change) : 0));

  // İlk ortalama kazanç ve kayıp
  let avgGain =
    gains.slice(0, rsiPeriod).reduce((a, b) => a + b, 0) / rsiPeriod;
  let avgLoss =
    losses.slice(0, rsiPeriod).reduce((a, b) => a + b, 0) / rsiPeriod;

  // Wilder's Smoothing: Sonraki dönemler için güncelleme
  for (let i = rsiPeriod; i < changes.length; i++) {
    avgGain = (avgGain * (rsiPeriod - 1) + gains[i]) / rsiPeriod;
    avgLoss = (avgLoss * (rsiPeriod - 1) + losses[i]) / rsiPeriod;
  }

  // RSI Hesaplama
  if (avgLoss === 0) {
    return 100; // Hiç kayıp yoksa RSI 100'dür
  }

  const rs = avgGain / avgLoss;
  return 100 - 100 / (1 + rs);
};

// Helper function to calculate RSI for any timeframe
const calculateRsiForTimeframe = async (
  symbol: string,
  interval: CandleChartInterval_LT
): Promise<number | null> => {
  try {
    const klineData = await klinesData(symbol, interval, TRADE_CONFIG.KLINE_LIMIT);
    if (!klineData) return null;

    const { closes } = klineData;
    return calculateRsi({
      closes,
      rsiPeriod: TRADE_CONFIG.RSI_PERIOD,
    });
  } catch (error: any) {
    console.error(`🔴 ${interval} RSI calculation error for ${symbol}:`, error);
    return null;
  }
};

// EMA calculation function
const calculateEMA = (prices: number[], period: number): number => {
  if (prices.length < period) {
    throw new Error(`EMA hesaplamak için en az ${period} fiyat gerekli.`);
  }

  const multiplier = 2 / (period + 1);
  let ema = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

  for (let i = period; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
  }

  return ema;
};


