/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from "next/server";
import { PositionMonitor } from "@/src/app/api/init/position-monitor";
import { DailyLimitsManager } from "@/src/app/api/init/daily-limits";

interface SchedulerState {
  isRunning: boolean;
  lastRun: number;
  runCount: number;
  errors: number;
  uptime: number;
  nextScheduledRun: number;
}

/**
 * Smart scheduler endpoint for continuous scalping bot operation
 * This endpoint should be called every 2 minutes via cron job
 * It intelligently decides when to trigger actual trading scans
 */
export async function GET() {
  try {
    const startTime = Date.now();
    
    console.log(`🤖 Scheduler Check - ${new Date().toISOString()}`);
    
    // Get current position stats and recommendation
    const recommendation = await PositionMonitor.getTradingRecommendation();
    const positionStats = await PositionMonitor.getPositionStats();
    const dailyLimits = await DailyLimitsManager.getDailyLimits();
    
    // Log comprehensive status
    console.log(`📊 Scheduler Status:`);
    console.log(`   Positions: ${positionStats.currentCount}/${positionStats.maxPositions} (${positionStats.fillRate.toFixed(1)}%)`);
    console.log(`   Daily P&L: ${dailyLimits.dailyPnL.toFixed(2)} USDT (${dailyLimits.tradesCount} trades)`);
    console.log(`   Recommendation: ${recommendation.action} (${recommendation.strategy})`);
    
    // Decide action based on recommendation
    let actionTaken = 'MONITOR';
    let scanResult = null;
    
    if (recommendation.action === 'SCAN') {
      console.log(`🔍 Triggering trading scan...`);
      
      try {
        // Call the main trading endpoint
        const baseUrl = getBaseUrl();
        const response = await fetch(`${baseUrl}/api/init`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (response.ok) {
          scanResult = await response.json();
          actionTaken = 'SCAN_EXECUTED';
          console.log(`✅ Scan completed: ${scanResult.data?.signalsFound || 0} signals found`);
        } else {
          console.error(`❌ Scan failed: ${response.status} ${response.statusText}`);
          actionTaken = 'SCAN_FAILED';
        }
      } catch (error) {
        console.error(`🔴 Scan error:`, error);
        actionTaken = 'SCAN_ERROR';
      }
    }
    
    // Calculate next run time
    const nextRunDelay = calculateNextRunDelay(recommendation, positionStats);
    const nextRun = Date.now() + nextRunDelay;
    
    const executionTime = Date.now() - startTime;
    
    // Return comprehensive status
    return NextResponse.json({
      success: true,
      scheduler: {
        action: actionTaken,
        executionTime: executionTime,
        nextRun: new Date(nextRun).toISOString(),
        nextRunDelay: Math.round(nextRunDelay / 1000 / 60), // minutes
      },
      recommendation: {
        action: recommendation.action,
        reason: recommendation.reason,
        strategy: recommendation.strategy,
        waitTime: Math.round(recommendation.waitTime / 1000 / 60), // minutes
      },
      positions: {
        current: positionStats.currentCount,
        max: positionStats.maxPositions,
        fillRate: Math.round(positionStats.fillRate),
        consecutiveEmptyScans: positionStats.consecutiveEmptyScans,
      },
      daily: {
        pnl: parseFloat(dailyLimits.dailyPnL.toFixed(4)),
        trades: dailyLimits.tradesCount,
        tradingActive: dailyLimits.tradingActive,
        target: 3.0,
        maxLoss: -2.0,
      },
      scanResult: scanResult ? {
        signals: scanResult.data?.signalsFound || 0,
        positions: scanResult.data?.activePositionsLength || 0,
        tradingStrategy: scanResult.data?.tradingStrategy || 'UNKNOWN',
      } : null,
      timestamp: new Date().toISOString(),
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('🔴 Scheduler error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Scheduler error occurred',
      details: error.message,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Calculate optimal delay until next scheduler run
 */
function calculateNextRunDelay(
  recommendation: any, 
  positionStats: any
): number {
  const baseDelay = 2 * 60 * 1000; // 2 minutes base interval
  
  // If we just scanned, wait a bit longer
  if (recommendation.action === 'SCAN') {
    return 5 * 60 * 1000; // 5 minutes after scan
  }
  
  // If positions are nearly full, check more frequently
  if (positionStats.fillRate > 80) {
    return baseDelay; // 2 minutes
  }
  
  // If we've had many empty scans, check less frequently
  if (positionStats.consecutiveEmptyScans >= 6) {
    return 10 * 60 * 1000; // 10 minutes
  }
  
  // Default interval
  return baseDelay;
}

/**
 * Get base URL for internal API calls
 */
function getBaseUrl(): string {
  // In production, use environment variable or fixed URL
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  
  if (process.env.NODE_ENV === 'production') {
    return 'https://your-domain.com'; // Replace with your domain
  }
  
  return 'http://localhost:3000';
}