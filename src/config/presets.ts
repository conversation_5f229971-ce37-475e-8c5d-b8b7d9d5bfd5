// Base type definition for stronger type safety
interface TradeConfig {
  TRADE_SYMBOL: string;
  TRADE_CURRENCY: string;
  BB_PERIOD: number;
  STD_DEV: number;
  RSI_PERIOD: number;
  MAX_POSITIONS: number;
  MIN_USDT_BALANCE: number;
  POSITION_SIZE: number;

  RSI_UP_THRESHOLD: number;
  RSI_DOWN_THRESHOLD: number;

  LEVERAGE: number;
  MARGIN_TYPE: "ISOLATED" | "CROSSED";
  STOP_LOSS_PERCENT: number;
  TAKE_PROFIT_PERCENT: number;
  VOLATILITY_THRESHOLD: number;

  KLINE_LIMIT: number;

  BB_PERIOD_15MIN: number;
  STD_DEV_15MIN: number;
  RSI_PERIOD_15MIN: number;
}

/**
 * Collection of predefined strategy presets. Feel free to tweak any value.
 */
const TRADE_PRESETS: Record<"aggressive" | "stable" | "conservative" | "optimizedScalp", TradeConfig> = {
  aggressive: {
    TRADE_SYMBOL: "BTCUSDT",
    TRADE_CURRENCY: "USDT",
    BB_PERIOD: 20,
    STD_DEV: 2.5,
    RSI_PERIOD: 14,
    MAX_POSITIONS: 10,
    MIN_USDT_BALANCE: 3,
    POSITION_SIZE: 1.5,

    RSI_UP_THRESHOLD: 75, // RSI üst eşiği - erken satış sinyali
    RSI_DOWN_THRESHOLD: 25, // RSI alt eşiği - erken alış sinyali

    LEVERAGE: 20,
    MARGIN_TYPE: "ISOLATED",
    STOP_LOSS_PERCENT: 6,
    TAKE_PROFIT_PERCENT: 2,
    VOLATILITY_THRESHOLD: 1.5,

    KLINE_LIMIT: 100,

    BB_PERIOD_15MIN: 20,
    STD_DEV_15MIN: 2.5,
    RSI_PERIOD_15MIN: 14,
  },

  optimizedScalp: {
    TRADE_SYMBOL: "BTCUSDT",
    TRADE_CURRENCY: "USDT",
    BB_PERIOD: 20,
    STD_DEV: 2.0, // Standard deviation for Bollinger Bands
    RSI_PERIOD: 14,
    MAX_POSITIONS: 20, // Reduced for better control and focus
    MIN_USDT_BALANCE: 10, // Lower balance requirement
    POSITION_SIZE: 1.5, // Fixed 1 USDT per position for consistent daily profit

    RSI_UP_THRESHOLD: 70, // These are overridden in analyze.ts
    RSI_DOWN_THRESHOLD: 30, // These are overridden in analyze.ts

    LEVERAGE: 10, // Optimal leverage for scalping
    MARGIN_TYPE: "ISOLATED",
    STOP_LOSS_PERCENT: 0.3, // Tighter stop-loss for better risk management
    TAKE_PROFIT_PERCENT: 0.8, // 1:2 risk/reward ratio for consistent profits
    VOLATILITY_THRESHOLD: 2, // Base threshold, dynamically adjusted

    KLINE_LIMIT: 100,

    BB_PERIOD_15MIN: 20, // For higher timeframe confirmation
    STD_DEV_15MIN: 2.0,
    RSI_PERIOD_15MIN: 14,
  },

  stable: {
    TRADE_SYMBOL: "BTCUSDT",
    TRADE_CURRENCY: "USDT",
    BB_PERIOD: 20,
    STD_DEV: 2.0,
    RSI_PERIOD: 14,
    MAX_POSITIONS: 8,
    MIN_USDT_BALANCE: 4,
    POSITION_SIZE: 1,

    RSI_UP_THRESHOLD: 70, // RSI üst eşiği - klasik overbought
    RSI_DOWN_THRESHOLD: 30, // RSI alt eşiği - klasik oversold

    LEVERAGE: 8,
    MARGIN_TYPE: "ISOLATED",
    STOP_LOSS_PERCENT: 4,
    TAKE_PROFIT_PERCENT: 1,
    VOLATILITY_THRESHOLD: 2.5,

    KLINE_LIMIT: 100,

    BB_PERIOD_15MIN: 20,
    STD_DEV_15MIN: 2.0,
    RSI_PERIOD_15MIN: 14,
  },

  conservative: {
    TRADE_SYMBOL: "BTCUSDT",
    TRADE_CURRENCY: "USDT",
    BB_PERIOD: 20,
    STD_DEV: 1.8,
    RSI_PERIOD: 14,
    MAX_POSITIONS: 4,
    MIN_USDT_BALANCE: 5,
    POSITION_SIZE: 0.5,

    RSI_UP_THRESHOLD: 80, // RSI üst eşiği - çok güçlü overbought
    RSI_DOWN_THRESHOLD: 20, // RSI alt eşiği - çok güçlü oversold

    LEVERAGE: 5,
    MARGIN_TYPE: "ISOLATED",
    STOP_LOSS_PERCENT: 3,
    TAKE_PROFIT_PERCENT: 0.8,
    VOLATILITY_THRESHOLD: 3.0,

    KLINE_LIMIT: 100,

    BB_PERIOD_15MIN: 20,
    STD_DEV_15MIN: 1.8,
    RSI_PERIOD_15MIN: 14,
  },
};

/**
 * Central place to switch strategies. Default can be overridden via env var
 * `NEXT_PUBLIC_TRADE_STRATEGY` (accepted values: aggressive, stable, conservative).
 */
type StrategyName = keyof typeof TRADE_PRESETS;

// Read from environment or fallback to desired default
export const CURRENT_STRATEGY: StrategyName = (process.env.NEXT_PUBLIC_TRADE_STRATEGY as StrategyName) || "optimizedScalp";

// This is what the rest of the codebase should import.
export const TRADE_CONFIG: TradeConfig = TRADE_PRESETS[CURRENT_STRATEGY];

export enum SignalType {
  LONG = "LONG",
  SHORT = "SHORT",
}

