/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Redis client for Vercel deployment
 * Uses Upstash Redis REST API for serverless compatibility
 */

interface RedisConfig {
  url: string;
  token: string;
}

export class RedisClient {
  private config: RedisConfig;

  constructor() {
    this.config = {
      url: process.env.UPSTASH_REDIS_REST_URL || '',
      token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
    };

    if (!this.config.url || !this.config.token) {
      console.warn('⚠️ Redis not configured, using fallback storage');
    }
  }

  /**
   * Check if Redis is available
   */
  isAvailable(): boolean {
    return !!(this.config.url && this.config.token);
  }

  /**
   * Set a key-value pair with optional expiration
   */
  async set(key: string, value: any, expirationSeconds?: number): Promise<boolean> {
    if (!this.isAvailable()) {
      console.warn(`Redis unavailable, skipping set: ${key}`);
      return false;
    }

    try {
      const body = expirationSeconds 
        ? ['SET', key, JSON.stringify(value), 'EX', expirationSeconds]
        : ['SET', key, JSON.stringify(value)];

      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      return response.ok;
    } catch (error) {
      console.error('Redis SET error:', error);
      return false;
    }
  }

  /**
   * Get a value by key
   */
  async get(key: string): Promise<any | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['GET', key])
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data.result ? JSON.parse(data.result) : null;
    } catch (error) {
      console.error('Redis GET error:', error);
      return null;
    }
  }

  /**
   * Delete a key
   */
  async del(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['DEL', key])
      });

      return response.ok;
    } catch (error) {
      console.error('Redis DEL error:', error);
      return false;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['EXISTS', key])
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.result === 1;
    } catch (error) {
      console.error('Redis EXISTS error:', error);
      return false;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key: string): Promise<number | null> {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['INCR', key])
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data.result;
    } catch (error) {
      console.error('Redis INCR error:', error);
      return null;
    }
  }

  /**
   * Get multiple keys at once
   */
  async mget(keys: string[]): Promise<(any | null)[]> {
    if (!this.isAvailable() || keys.length === 0) {
      return keys.map(() => null);
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['MGET', ...keys])
      });

      if (!response.ok) {
        return keys.map(() => null);
      }

      const data = await response.json();
      return (data.result || []).map((item: string | null) => 
        item ? JSON.parse(item) : null
      );
    } catch (error) {
      console.error('Redis MGET error:', error);
      return keys.map(() => null);
    }
  }

  /**
   * Health check
   */
  async ping(): Promise<boolean> {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.url}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(['PING'])
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.result === 'PONG';
    } catch (error) {
      console.error('Redis PING error:', error);
      return false;
    }
  }
}

// Singleton instance
export const redis = new RedisClient();