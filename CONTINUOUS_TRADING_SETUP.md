# 🤖 <PERSON><PERSON><PERSON><PERSON> Çalışan Scalping Bot Kurulumu

## 📋 Genel Bakış

Bu sistem artık **akıllı adaptif tarama** ile çalışıyor. Bot kendisi karar veriyor ne zaman tarama yapacağına:

- **15 dakika** - Normal durumda (pozisyonlar %50'nin altında)
- **5 dakika** - Pozisyonlar %50-80 dolulukta  
- **2 dakika** - Pozisyonlar %80+ dolulukta
- **30 dakika** - Çok fazla boş tarama sonrası (backoff)

## 🔧 Sistem Bileşenleri

### 1. **Ana Endpoint'ler**
```bash
# Scheduler (Ana kontrol merkezi)
/api/scheduler - Her 2 dakikada çağrılacak

# Trading (Otomatik tarama)
/api/init - Scheduler tarafından otomatik çağrılır

# Trading (Manuel tarama)
/api/init?manual=true - Position monitor kontrollerini atlar

# Health Check (Sistem durumu)
/api/health - İsteğe bağlı monitoring
```

### 2. **Ak<PERSON>ll<PERSON> Karar Sistemi**
- **WAIT**: Henüz erken, bekle
- **SCAN**: Şimdi tarama yap
- **MONITOR**: Max pozisyonda, sadece izle

## 🚀 Kurulum

### **Adım 1: Crontab Ayarlama**
```bash
# Crontab editörünü aç
crontab -e

# Her 2 dakikada scheduler'ı çalıştır
*/2 * * * * curl -s "http://localhost:3000/api/scheduler" >> /var/log/scalping-bot.log 2>&1

# İsteğe bağlı: Her 30 dakikada health check
*/30 * * * * curl -s "http://localhost:3000/api/health" >> /var/log/health.log 2>&1
```

### **Adım 2: Next.js'i Production'da Çalıştır**
```bash
# Build yap
npm run build

# Production modda başlat
npm run start

# Ya da PM2 ile (önerilen)
npm install -g pm2
pm2 start npm --name "scalping-bot" -- start
pm2 save
pm2 startup
```

### **Adım 3: Log Monitoring**
```bash
# Canlı log takibi
tail -f /var/log/scalping-bot.log

# Health durumu kontrol
curl "http://localhost:3000/api/health" | jq
```

## 📊 Sistem Davranışları

### **Pozisyon Doluluk Oranına Göre**

```
🟢 0-30%   → AGGRESSIVE  (15 dakikada tarama)
🟡 30-70%  → MODERATE    (8 dakikada tarama)  
🟠 70-90%  → CONSERVATIVE (5 dakikada tarama)
🔴 90-100% → MONITOR     (2 dakikada kontrol)
```

### **Boş Tarama Sonrası Backoff**
```
1-3 boş tarama  → Normal interval
4-6 boş tarama  → %50 yavaşlama
6+ boş tarama   → 30 dakika bekle
```

## 📈 Monitoring ve Kontrol

### **Real-time Status Check**
```bash
# Anlık durum
curl "http://localhost:3000/api/scheduler" | jq '.positions'

# Günlük kar/zarar
curl "http://localhost:3000/api/scheduler" | jq '.daily'

# Sistem sağlığı
curl "http://localhost:3000/api/health" | jq '.health.overall'

# Manuel tarama (position monitor kontrollerini atlar)
curl "http://localhost:3000/api/init?manual=true" | jq
```

### **Beklenen Log Çıktıları**
```
📊 Position Monitor Status:
   Positions: 2/15 (13.3%)
   Recommendation: SCAN (AGGRESSIVE)
   Reason: Position fill rate: 13.3%
   Empty scans: 0
   Next check in: 15 minutes

🔍 Triggering trading scan...
✅ Scan completed: 3 signals found
🏁 Trading completed. Opened 2 new positions
```

## ⚠️ Günlük Limitler

**Otomatik Durdurma Koşulları:**
- **+3 USDT kar** → Trading durdurulur
- **-2 USDT zarar** → Trading durdurulur  
- **200 işlem** → Günlük limit aşılırsa durdur

## 🛠️ Troubleshooting

### **Problem: Bot çalışmıyor**
```bash
# Scheduler durumunu kontrol et
curl "http://localhost:3000/api/scheduler"

# Health check yap
curl "http://localhost:3000/api/health"

# Crontab'ı kontrol et
crontab -l
```

### **Problem: Çok sık/az tarama yapıyor**
```bash
# Position monitor loglarını kontrol et
curl "http://localhost:3000/api/scheduler" | jq '.recommendation'

# Manuel tarama test et
curl "http://localhost:3000/api/init"
```

### **Problem: Pozisyonlar açılmıyor**
```bash
# Günlük limitler kontrol et
curl "http://localhost:3000/api/scheduler" | jq '.daily'

# Binance bağlantı test et
curl "http://localhost:3000/api/health" | jq '.components.binanceConnection'
```

## 📱 Telegram/Discord Entegrasyonu (İsteğe Bağlı)

```bash
# Günlük özet için webhook
0 23 * * * curl "http://localhost:3000/api/scheduler" | jq '.daily' | curl -X POST -H 'Content-type: application/json' --data @- YOUR_WEBHOOK_URL
```

## 🔄 Güncelleme ve Bakım

```bash
# Sistemi güncelle
git pull origin claude-code
npm install
npm run build

# PM2 restart
pm2 restart scalping-bot

# Logs temizle
> /var/log/scalping-bot.log
> /var/log/health.log
```

## 📋 Günlük Kontrol Listesi

**Her Sabah:**
1. ✅ Health check: `curl localhost:3000/api/health`
2. ✅ Günlük P&L: `curl localhost:3000/api/scheduler | jq '.daily'`
3. ✅ Pozisyon durumu: `curl localhost:3000/api/scheduler | jq '.positions'`
4. ✅ Log dosyası boyutu: `ls -lh /var/log/scalping-bot.log`

**Haftalık:**
1. 🔄 PM2 restart: `pm2 restart scalping-bot`
2. 🧹 Log temizliği: `logrotate`
3. 📊 Performans analizi
4. 🔧 Parametre ince ayarı

Bu sistem artık tamamen otonom çalışacak ve piyasa koşullarına göre kendini optimize edecek! 🚀